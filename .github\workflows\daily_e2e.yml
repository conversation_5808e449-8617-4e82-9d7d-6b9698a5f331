name: Daily E2E Runner

on:
  schedule:
    - cron: '0 12 * * 2' # Tuesday, 7 AM EST
    - cron: '0 12 * * 4' # Thursday, 7 AM EST
    - cron: '0 12 * * 6' # Saturday, 7 AM EST
  workflow_dispatch:

env:
  GITHUB_ACCESS_TOKEN: ${{ secrets.GITHUB_TOKEN }}

jobs:
  run_daily_tasks:
    runs-on: veritone-self-hosted-16gb

    defaults:
      run:
        shell: bash

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install Cypress dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y libnss3-tools \
            wget libgtk2.0-0 libgtk-3-0 libgbm-dev libnotify-dev libgconf-2-4 libnss3 libxss1 \
            libasound2 libxtst6 xauth xvfb \
            libpango-1.0-0 libpangoft2-1.0-0 libpangocairo-1.0-0 libcairo2-dev libjpeg-dev \
            libgif-dev librsvg2-dev build-essential python3 pkg-config
          # Try to locate pangocairo and set PKG_CONFIG_PATH
          if find /usr/lib* -name "pangocairo.pc" -print0 | xargs -0 dirname | grep -q .; then
            export PKG_CONFIG_PATH=$(find /usr/lib* -name "pangocairo.pc" -print0 | xargs -0 dirname | tr '\n' ':'):$PKG_CONFIG_PATH
            echo "PKG_CONFIG_PATH set to include pangocairo location."
          fi
          curl -sSL https://dl.filippo.io/mkcert/latest?for=linux/amd64 -o mkcert
          sudo mv mkcert /usr/local/bin/
          sudo chmod +x /usr/local/bin/mkcert

      - name: Configure Git authentication
        run: |
          git config --global url."https://${{ env.GITHUB_ACCESS_TOKEN }}:<EMAIL>/".insteadOf "https://github.com/"
          git remote set-url origin https://github.com/veritone/redact-app.git

      - name: Configure npm authentication
        run: |
          echo "@veritone:registry=https://npm.pkg.github.com" >> ~/.npmrc
          echo "//npm.pkg.github.com/:_authToken=${{ env.GITHUB_ACCESS_TOKEN }}" >> ~/.npmrc

      - name: Create and Trust mkcert Certificate
        run: |
          mkcert -install
          mkcert local.veritone.com
          # Trust certificate for Chrome
          CAROOT=$(mkcert -CAROOT)
          mkdir -p $HOME/.pki/nssdb
          certutil -N --empty-password -d "sql:$HOME/.pki/nssdb"
          certutil -d "sql:$HOME/.pki/nssdb" -A -t "C,," -n "mkcert" -i "$CAROOT/rootCA.pem"

      - name: Configure Hosts
        run: |
          echo "127.0.0.1 local.veritone.com" | sudo tee -a /etc/hosts

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22.x'
          cache: 'npm'

      - name: Corepack Setup
        run: |
          npm install -g corepack@latest
          corepack enable
          corepack prepare yarn@4.7.0 --activate

      - name: Install Dependencies
        run: |
          yarn

      - name: Install Chrome
        run: |
          wget https://dl.google.com/linux/direct/google-chrome-stable_current_amd64.deb
          sudo apt install -y ./google-chrome*.deb
          sudo apt-get install -f -y

      - name: Start Webpack Server
        run: |
          echo "Starting Webpack server..."
          nohup yarn startssl:cypress > app_startup.log 2>&1 &
          echo $! > webpack.pid
          sleep 30
          tail -n 10 app_startup.log

      - name: Wait for App
        run: |
          curl -k https://local.veritone.com:3001

      - name: Create cypress.env.json
        run: |
          echo '{
            "username": "${{ secrets.CYPRESS_USER1_USERNAME }}",
            "password": "${{ secrets.CYPRESS_USER1_PASSWORD }}"
            }' > cypress.env.json
      
      - name: Create config file for azure stage
        run: cp config_v3f.azure_stage.js static/config.js
        
      - name: Run Health Check
        id: health_check
        uses: cypress-io/github-action@v6
        with:
          command: yarn cy:run --spec=cypress/e2e/features/health-check.feature
          browser: chrome
          config: chromeWebSecurity=false
        continue-on-error: true

      - name: Check Health Check Results
        id: health_check_status
        run: |
          if [ "${{ steps.health_check.outcome }}" = "success" ]; then
            echo "health_check_passed=true" >> $GITHUB_OUTPUT
            echo "Health check passed - proceeding with full test suite"
          else
            echo "health_check_passed=false" >> $GITHUB_OUTPUT
            echo "Health check failed - skipping main Cypress tests"
          fi

      - name: Run Cypress
        uses: cypress-io/github-action@v6
        if: steps.health_check_status.outputs.health_check_passed == 'true'
        with:
          command: yarn cy:run:exclude-health
          browser: chrome
          config: chromeWebSecurity=false
        continue-on-error: true

      - name: Parse Cucumber JSON Report
        id: parse_report
        if: always()
        run: |
          chmod +x .github/scripts/parse-report.sh
          .github/scripts/parse-report.sh  
      
      - name: Send Slack Notification
        uses: rtCamp/action-slack-notify@v2

        if: always()
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_USERNAME: Cypress E2E Bot
          SLACK_CHANNEL: team-dev-glc
          SLACK_MESSAGE: |
            ${{ steps.parse_report.outputs.SLACK_MESSAGE }}

            *Health Check Status:* ${{ steps.health_check.outcome == 'success' && '✅ PASSED' || '❌ FAILED' }}
            ${{ steps.health_check_status.outputs.health_check_passed == 'false' && '*Main tests were skipped due to health check failure*' || '' }}
          SLACK_COLOR: ${{ (steps.parse_report.outputs.SLACK_MESSAGE_STATUS == 'FAILURE' || steps.health_check.outcome == 'failure') && 'danger' || 'good' }}    

      - name: Cleanup
        if: always()
        run: |
          echo "Cleaning up background processes..."
          if [ -f webpack.pid ]; then
            if kill -0 $(cat webpack.pid) 2>/dev/null; then
              echo "Killing Webpack server process $(cat webpack.pid)"
              kill $(cat webpack.pid) || true
            else
              echo "Webpack server process $(cat webpack.pid) not found or already stopped."
            fi
            rm -f webpack.pid
          else
            echo "webpack.pid file not found. No process to kill."
          fi
