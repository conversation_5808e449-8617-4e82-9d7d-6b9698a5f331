import Konva from 'konva';
import { map } from 'rxjs/operators';

import { search } from '@worker';
import { aToB } from '../../Playhead/renderer/utils';
import { MapRenderProps } from '../../renderer';
import { ClusterSegment } from '@common/state/modules/mediaDetails/models';

function getOffsetTime(currentTime: number, videoOffset: number) {
  return Math.max(currentTime - videoOffset, 0);
}

export const mapRender = (layer: Konva.Layer) =>
  map(
    ({
      startMs,
      stopMs,
      collection,
      selected,
      width,
      videoOffset,
      udrClusterGroups,
      highlightedUdrGroupIds,
    }: MapRenderProps) => {
      layer.destroyChildren();

      const p2m = aToB(0, width, startMs, stopMs);

      let rect: Konva.Rect | undefined;
      let rectUnselected: Konva.Rect | undefined;
      let midLine: Konva.Rect | undefined;

      let preCurrentTime = 0;
      for (let px = 0; px < width; px++) {
        const currentTime = getOffsetTime(p2m(px), videoOffset);

        const polys = search(
          collection?.collection,
          currentTime,
          !!rect,
          preCurrentTime
        );
        const isHit = polys.some((p) => selected[p.id] && p.type === 'udr');

        if (isHit && !rect) {
          // close any rectUnselected
          if (rectUnselected) {
            rectUnselected.setSize({
              width: px - rectUnselected.x(),
              height: 32,
            });
            layer.add(rectUnselected);
            rectUnselected = undefined;
          }

          rect = new Konva.Rect({
            x: px,
            y: 0,
            fill: 'rgb(255, 235, 59)',
          });
        } else if ((!isHit || px === width - 1) && rect) {
          rect.setSize({ width: px - rect.x(), height: 32 });
          layer.add(rect);
          rect = undefined;
        }
        preCurrentTime = currentTime;

        // draw unselected
        if (!isHit) {
          const isUnselected = polys.some(
            (p) => !selected[p.id] && p.type === 'udr'
          );
          if (isUnselected && !rectUnselected) {
            rectUnselected = new Konva.Rect({
              x: px,
              y: 0,
              fill: 'rgb(255, 235, 59)',
              opacity: 0.1,
            });
          } else if ((!isUnselected || px === width - 1) && rectUnselected) {
            rectUnselected.setSize({
              width: px - rectUnselected.x(),
              height: 32,
            });
            layer.add(rectUnselected);
            rectUnselected = undefined;
          }
        }

        if (udrClusterGroups && highlightedUdrGroupIds) {
          let segments: ClusterSegment[] = [];
          highlightedUdrGroupIds.forEach((id) => {
            const group = udrClusterGroups[id];
            if (group) {
              segments = segments.concat(group.segments);
            }
          });
          if (segments.length > 0) {
            for (let px = 0; px < width; px++) {
              const currentTime = getOffsetTime(p2m(px), videoOffset);
              const isSelected = segments
                .filter(
                  (p) =>
                    p.startTimeMs <= currentTime && p.stopTimeMs >= currentTime
                )
                .some((p) => !!selected[p.id]);

              if (isSelected && !midLine) {
                midLine = new Konva.Rect({
                  x: px,
                  y: 10,
                  fill: 'white',
                  cornerRadius: 4,
                });
              } else if ((!isSelected || px === width - 1) && midLine) {
                midLine.setSize({
                  width: px - midLine.x(),
                  height: 8,
                });
                layer.add(midLine);
                midLine = undefined;
              }
            }
          }
        }
      }

      return layer;
    }
  );
