declare namespace Cypress {
  interface Chainable {
    Graphql<T = unknown>(
      query: string,
      variables?: Record<string, unknown>
    ): Chainable<GraphqlResponse<T>>;
    request<T = unknown>(
      options: Partial<RequestOptions>
    ): Chainable<Response<T>>;
    LoginToApp(): Chainable;
    LoginLandingPage(): Chainable<void>;
    GoToTestFile(fileName: string): Chainable<void>;
    ResetAndGoToTestFile(fileName: string): Chainable;
    DrawAnUdr(coordinates: {
      orderNumber: number;
      x1: number;
      y1: number;
      x2: number;
      y2: number;
    }): Chainable<void>;
    DeleteUDRGroups(): Chainable<void>;
    OpenUploadFileModal(): Chainable<void>;
    ClickToUpload(): Chainable<void>;
    UploadFileWithoutEngine({
      videoName,
    }: {
      videoName: string;
    }): Chainable<void>;
    SelectFile({ videoName }: { videoName: string }): Chainable<void>;
    getByRoles(role: string): Chainable<JQuery<HTMLElement>>;
    CheckTDOWrapperOfNewUploadFile(): Chainable<void>;
    UploadFileWithEngine({
      videoName,
      transcriptionState,
    }: {
      videoName: string;
      transcriptionState?: string;
    }): Chainable<void>;
    OpenUploadFileModal(): Chainable<void>;
    ClickToUpload(): Chainable<void>;
    filterByAria(attr: string, value: string): Chainable<void>;
    CheckJobComplete({
      videoName,
      jobName,
    }: {
      videoName: string;
      jobName: string;
    }): Chainable<void>;
    CheckJobCompleteDashboard({
      videoName,
      jobName,
    }: {
      videoName: string;
      jobName: string;
    }): Chainable<void>;
    DrawCustomUDRsWithIncreasingCoordinates(count: number): Chainable<void>;
    StopVideoAtSeconds(seconds: number): Chainable<void>;
    /**
     * Returns undefined if the input is not a valid ordinal.
     * @param ordinal The ordinal string to convert.
     */
    ordinalToNumber(ordinal: string): number | undefined;
    repeat({
      action,
      times,
    }: {
      action: unknown;
      times: number;
    }): Chainable<void>;
    awaitNetworkResponseCode({
      alias,
      code,
      repeat,
    }: {
      alias: string;
      code: number;
      repeat?: number;
    }): Chainable<void>;
    getDataSetCy({
      cyAlias,
      options,
    }: {
      cyAlias: string;
      options?: any;
    }): Cypress.Chainable<JQuery<HTMLElement>>;
    getDataIdCy({
      idAlias,
      options,
    }: {
      idAlias: string;
      options?: any;
    }): Cypress.Chainable<JQuery<HTMLElement>>;
    navigateToSectionByName({
      sectionName,
    }: {
      sectionName: string;
    }): Chainable<void>;
    interceptGraphQLQuery(query: string, alias: string): Chainable<void>;
    deleteRedactionCodeIfExist(redactionCodeName: string): Chainable<void>;
    deleteProfileByName(name: string): Chainable<void>;
    toHaveCssProperty(attr: string, value?: string): Chainable<void>;
    toNotHaveCssProperty(attr: string, value?: string): Chainable<void>;
    getState(): Chainable<any>;
    deleteAllRedacted(tdoId: string): Chainable;
    clearTrimRange(tdoId: string): Chainable;
    setKeyValue(keyName: string, value: string | number): Chainable<void>;
    getKeyValue(keyName: string): Chainable<string | number>;
    assertNoLoading(): Chainable;
    deleteTdoByName(nameList: string[]): Chainable;
  }
}

interface JobCompletionArgs {
  videoName?: string;
  jobName?: string;
}

interface coordinate {
  orderNumber: number;
  x1: number;
  y1: number;
  x2: number;
  y2: number;
}

interface GraphqlResponse<T> {
  body: {
    data: T;
  };
}

interface FetchRedactionCodeResponse {
  structuredDataObjects: {
    records: Array<{
      data: {
        codeName: string;
      };
      id: string;
    }>;
  };
}

interface DeleteRedactionCodeResponse {
  deleteStructuredData: {
    id: string;
  };
}

interface FetchProfilesResponse {
  structuredDataObjects: {
    records: Array<{
      data: {
        profileName: string;
      };
      id: string;
    }>;
  };
}

interface DeleteProfileResponse {
  deleteStructuredData: {
    id: string;
  };
}

interface TDODownloadResponse {
  temporalDataObject: {
    assets: {
      records: {
        id: string;
        name: string;
        contentType: string;
        modifiedDateTime: string;
        assetType: string;
        signedUri: string;
        details: Record<string, any>;
        sourceData: {
          taskId: string;
          sourceId: string;
        };
      }[];
    };
  };
}

interface UpdateSettingsTDOResponse {
  updateTDO: {
    id: string;
    details: Record<string, any>;
  };
}

interface TDODetailResponse {
  temporalDataObject: {
    name: string;
    status: 'downloaded' | 'recording' | 'recorded';
    thumbnailUrl: string;
    modifiedDateTime: string;
    startDateTime: string;
    stopDateTime: string;
    details: {
      name?: string;
      isExport?: boolean;
    } & Record<string, any>;
    primaryAsset: {
      readonly id: string;
      readonly name: string | null;
      readonly description: string | null;
      readonly signedUri: string;
      readonly details?: {
        readonly virtualAsset?: boolean;
      };
      readonly jsondata: {
        readonly mediaDuration?: number;
      };
      readonly contentType: string;
    };
    redactedMediaAssets: {
      records: {
        id: string;
        name: string;
        contentType: string;
        modifiedDateTime: string;
        signedUri: string;
        details: Record<string, any>;
        fileData: {
          md5sum: string;
        };
      }[];
    };
    auditLogAssets: {
      records: {
        id: string;
        name: string;
        contentType: string;
        signedUri: string;
        details: Record<string, any>;
      }[];
    };
    redactExportAssets: {
      records: {
        id: string;
        name: string;
        contentType: string;
        signedUri: string;
        details: Record<string, any>;
      }[];
    };
    waveformAssets: {
      records: {
        id: string;
        name: string;
        contentType: string;
        modifiedDateTime: string;
        signedUri: string;
        details: Record<string, any>;
        fileData: {
          md5sum: string;
        };
      }[];
    };
    streams: Array<{
      protocol: 'hls' | 'dash';
      uri: string;
    }>;
  };
}

interface FetchOrganizationSDOsResponse {
  structuredDataObjects: {
    records: {
      id: string;
      data: Record<string, any>;
      createdDateTime: string;
      modifiedDateTime: string;
    }[];
  };
}

interface FetchOrganizationTDOsResponse {
  id: number;
  name: string;
  status: 'downloaded' | 'recording' | 'recorded';
  thumbnailUrl: string;
  createdDateTime: string;
  modifiedDateTime: string;
  startDateTime: string;
  stopDateTime: string;
  details: {
    readonly tags?: ReadonlyArray<{
      redactionStatus: 'Draft' | 'Pending Review' | 'Complete';
      value: 'in redaction';
      toBeDeletedTime?: string;
    }>;
    readonly govQARequestId?: string;
    readonly foiaXpressRequestId?: string;
    readonly casepointRequestId?: string;
    readonly nuixRequestId?: string;
    readonly exterroRequestId?: string;
  };
  tasks: {
    records: ReadonlyArray<{
      id: string;
      createdDateTime: string;
      startedDateTime: string | null;
      completedDateTime: string | null;
      engineId: string;
      engine: {
        id: string;
        categoryId: string;
        name: string;
      };
      jobId: string;
    }>;
  };
}
