// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`React testing library test renders correctly 1`] = `
<DocumentFragment>
  <div
    class="buttonsContainer"
    data-test="mainpage-tdo-topbarbtt"
    data-veritone-component="top-bar-button"
  >
    <div
      class="sortByButtonLabel"
      color="primary"
      data-veritone-element="sort-by-button"
    >
      Last Modified Date 
      <svg
        aria-hidden="true"
        class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-z5a59h-MuiSvgIcon-root"
        data-testid="ArrowDropDownIcon"
        focusable="false"
        viewBox="0 0 24 24"
      >
        <path
          d="m7 10 5 5 5-5z"
        />
      </svg>
    </div>
    <p
      class="sortByLabel"
    >
      Sort by:
    </p>
  </div>
</DocumentFragment>
`;
