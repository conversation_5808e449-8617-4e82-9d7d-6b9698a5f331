{"compileOnSave": false, "compilerOptions": {"target": "es2023", "module": "ESNext", "lib": ["es2023", "dom"], "skipLibCheck": true, "allowJs": true, "jsx": "react-jsx", "declaration": false, "sourceMap": true, "removeComments": true, "importHelpers": true, "downlevelIteration": true, "resolveJsonModule": true, "strict": false, "noImplicitAny": false, "strictNullChecks": false, "strictFunctionTypes": false, "strictPropertyInitialization": false, "noImplicitThis": false, "noUnusedLocals": false, "noUnusedParameters": false, "moduleResolution": "node", "baseUrl": "../", "rootDir": "../", "typeRoots": ["../node_modules/@types", "../node_modules/cypress/types"], "types": ["cypress", "node"], "allowSyntheticDefaultImports": true, "esModuleInterop": true, "preserveConstEnums": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "paths": {"@cbsa/*": ["src/cbsa-app/*"], "@common/*": ["src/common-app/*"], "@redact/*": ["src/redact-app/*"], "@resources/*": ["resources/*"], "@cbsa-reduxElements": ["src/cbsa-app/reduxElements/index"], "@cbsa-reduxElements/*": ["src/cbsa-app/reduxElements/*"], "@cbsa-components/*": ["src/cbsa-app/components/*"], "@common-components/*": ["src/common-app/components/*"], "@redact-components/*": ["src/redact-app/components/*"], "@helpers/*": ["src/helpers/*"], "@cbsa-modules/*": ["src/cbsa-app/state/modules/*"], "@common-modules/*": ["src/common-app/state/modules/*"], "@redact-modules/*": ["src/redact-app/state/modules/*"], "@cbsa-pages/*": ["src/cbsa-app/pages/*"], "@common-pages/*": ["src/common-app/pages/*"], "@redact-pages/*": ["src/redact-app/pages/*"], "@redact-routing": ["src/redact-app/state/modules/routing/index"], "@redact-routing/*": ["src/redact-app/state/modules/routing/*"], "@cbsa-state/*": ["src/cbsa-app/state/*"], "@cbsa-state": ["src/cbsa-app/state/index"], "@common-state/*": ["src/common-app/state/*"], "@common-state": ["src/common-app/state/index"], "@redact-state/*": ["src/redact-app/state/*"], "@redact-state": ["src/redact-app/state/index"], "@utils": ["src/utils/index"], "@redact-shared/*": ["src/redact-app/shared/*"], "@user-onboarding/components/*": ["src/common-app/user-onboarding/components/*"], "@user-onboarding": ["src/common-app/user-onboarding/index"], "@user-permissions": ["src/common-app/user-permissions/index"], "@worker/*": ["src/common-app/web-worker/*"], "@worker": ["src/common-app/web-worker/index"], "@redact-test/*": ["test/redact-app/*"], "@test/*": ["test/*"], "@i18n": ["src/common-app/i18n/index"]}}, "include": ["**/*.ts", "**/*.tsx", "../node_modules/cypress/types/index.d.ts"], "exclude": ["reports/**"]}